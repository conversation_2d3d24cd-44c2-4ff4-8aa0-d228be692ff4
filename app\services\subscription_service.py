from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal
from ..models.subscription import (
    Subscription, SubscriptionStatus, SubscriptionPlan, CustomPlan,
    Feature, BillingCycle, FeatureType, PlanVisibility
)
from ..models.subscription_usage import SubscriptionUsage, SubscriptionInvoice
from ..models.user import User
import logging
import uuid

logger = logging.getLogger(__name__)

class SubscriptionService:
    """Advanced service for managing user subscriptions, plans, and features"""

    # Plan Management Methods
    @staticmethod
    def create_plan(
        name: str,
        display_name: str,
        description: str = "",
        pricing_tiers: List[Dict] = None,
        features: List[Dict] = None,
        visibility: str = "public",
        is_active: bool = True
    ) -> SubscriptionPlan:
        """Create a new subscription plan"""
        try:
            from ..models.subscription import PricingTier, PlanFeature

            plan = SubscriptionPlan(
                name=name,
                display_name=display_name,
                description=description,
                visibility=PlanVisibility(visibility),
                is_active=is_active
            )

            # Add pricing tiers
            if pricing_tiers:
                for tier_data in pricing_tiers:
                    tier = PricingTier(
                        billing_cycle=BillingCycle(tier_data['billing_cycle']),
                        price=Decimal(str(tier_data['price'])),
                        currency=tier_data.get('currency', 'USD'),
                        trial_days=tier_data.get('trial_days', 0),
                        setup_fee=Decimal(str(tier_data.get('setup_fee', 0)))
                    )
                    plan.pricing_tiers.append(tier)

            # Add features
            if features:
                for feature_data in features:
                    feature = Feature.objects(name=feature_data['feature_name']).first()
                    if feature:
                        plan_feature = PlanFeature(
                            feature=feature,
                            value=str(feature_data['value']),
                            is_unlimited=feature_data.get('is_unlimited', False)
                        )
                        plan.features.append(plan_feature)

            return plan.save()
        except Exception as e:
            logger.error(f"Error creating plan: {str(e)}")
            raise

    @staticmethod
    def get_plan(plan_id: str) -> Optional[SubscriptionPlan]:
        """Get a subscription plan by ID"""
        try:
            return SubscriptionPlan.objects.get(id=plan_id)
        except Exception as e:
            logger.error(f"Error fetching plan: {str(e)}")
            return None

    @staticmethod
    def get_all_plans(include_inactive: bool = False) -> List[SubscriptionPlan]:
        """Get all subscription plans"""
        try:
            query = SubscriptionPlan.objects
            if not include_inactive:
                query = query.filter(is_active=True)
            return list(query.order_by('sort_order', 'name'))
        except Exception as e:
            logger.error(f"Error fetching plans: {str(e)}")
            return []

    @staticmethod
    def get_public_plans() -> List[SubscriptionPlan]:
        """Get all public subscription plans"""
        try:
            return list(SubscriptionPlan.objects(
                is_active=True,
                visibility=PlanVisibility.PUBLIC
            ).order_by('sort_order', 'name'))
        except Exception as e:
            logger.error(f"Error fetching public plans: {str(e)}")
            return []

    @staticmethod
    def update_plan(plan_id: str, **kwargs) -> Optional[SubscriptionPlan]:
        """Update a subscription plan"""
        try:
            plan = SubscriptionPlan.objects.get(id=plan_id)

            for key, value in kwargs.items():
                if hasattr(plan, key):
                    setattr(plan, key, value)

            plan.updated_at = datetime.utcnow()
            return plan.save()
        except Exception as e:
            logger.error(f"Error updating plan: {str(e)}")
            return None

    @staticmethod
    def delete_plan(plan_id: str) -> bool:
        """Delete a subscription plan (soft delete by setting inactive)"""
        try:
            plan = SubscriptionPlan.objects.get(id=plan_id)
            plan.is_active = False
            plan.visibility = PlanVisibility.ARCHIVED
            plan.updated_at = datetime.utcnow()
            plan.save()
            return True
        except Exception as e:
            logger.error(f"Error deleting plan: {str(e)}")
            return False

    # Feature Management Methods
    @staticmethod
    def create_feature(
        name: str,
        display_name: str,
        description: str = "",
        feature_type: str = "limit",
        default_value: str = "0",
        unit: str = ""
    ) -> Feature:
        """Create a new feature"""
        try:
            feature = Feature(
                name=name,
                display_name=display_name,
                description=description,
                feature_type=FeatureType(feature_type),
                default_value=default_value,
                unit=unit
            )
            return feature.save()
        except Exception as e:
            logger.error(f"Error creating feature: {str(e)}")
            raise

    @staticmethod
    def get_all_features(include_inactive: bool = False) -> List[Feature]:
        """Get all features"""
        try:
            query = Feature.objects
            if not include_inactive:
                query = query.filter(is_active=True)
            return list(query.order_by('name'))
        except Exception as e:
            logger.error(f"Error fetching features: {str(e)}")
            return []

    # Subscription Management Methods
    @staticmethod
    def create_subscription(
        user_id: str,
        plan_id: str = None,
        custom_plan_id: str = None,
        billing_cycle: str = "monthly",
        trial_days: int = 0
    ) -> Subscription:
        """Create a new subscription for a user"""
        try:
            user = User.objects.get(id=user_id)

            # Cancel any existing active subscription
            existing_sub = Subscription.objects(
                user=user,
                status__in=[SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING]
            ).first()

            if existing_sub:
                existing_sub.status = SubscriptionStatus.CANCELED
                existing_sub.canceled_at = datetime.utcnow()
                existing_sub.save()

            # Get plan details
            plan = None
            custom_plan = None
            plan_name = ""
            amount = Decimal('0')

            if plan_id:
                plan = SubscriptionPlan.objects.get(id=plan_id)
                plan_name = plan.display_name
                # Get price for billing cycle
                for tier in plan.pricing_tiers:
                    if tier.billing_cycle.value == billing_cycle:
                        amount = tier.price
                        break
            elif custom_plan_id:
                custom_plan = CustomPlan.objects.get(id=custom_plan_id)
                plan_name = custom_plan.name
                amount = custom_plan.monthly_total if billing_cycle == 'monthly' else custom_plan.yearly_total

            # Calculate dates
            start_date = datetime.utcnow()
            trial_end_date = None

            if trial_days > 0:
                trial_end_date = start_date + timedelta(days=trial_days)
                status = SubscriptionStatus.TRIALING
            else:
                status = SubscriptionStatus.ACTIVE

            # Calculate end date based on billing cycle
            if billing_cycle == 'monthly':
                end_date = start_date + timedelta(days=30)
                next_billing_date = end_date
            elif billing_cycle == 'yearly':
                end_date = start_date + timedelta(days=365)
                next_billing_date = end_date
            else:
                end_date = start_date + timedelta(days=30)
                next_billing_date = end_date

            # Create subscription
            subscription = Subscription(
                user=user,
                plan=plan,
                custom_plan=custom_plan,
                plan_name=plan_name,
                status=status,
                billing_cycle=BillingCycle(billing_cycle),
                start_date=start_date,
                end_date=end_date,
                trial_end_date=trial_end_date,
                next_billing_date=next_billing_date,
                amount=amount,
                current_usage={}
            )

            return subscription.save()
        except Exception as e:
            logger.error(f"Error creating subscription: {str(e)}")
            raise

    @staticmethod
    def get_subscription(user_id: str) -> Optional[Subscription]:
        """Get the active subscription for a user"""
        try:
            user = User.objects.get(id=user_id)
            return Subscription.objects(
                user=user,
                status__in=[SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING]
            ).first()
        except Exception as e:
            logger.error(f"Error fetching subscription: {str(e)}")
            return None

    @staticmethod
    def upgrade_subscription(user_id: str, new_plan_id: str, billing_cycle: str = None) -> Optional[Subscription]:
        """Upgrade user's subscription to a new plan"""
        try:
            current_sub = SubscriptionService.get_subscription(user_id)
            if not current_sub:
                return None

            new_plan = SubscriptionPlan.objects.get(id=new_plan_id)
            if not billing_cycle:
                billing_cycle = current_sub.billing_cycle.value

            # Calculate prorated amount and new billing
            # For simplicity, we'll create a new subscription
            return SubscriptionService.create_subscription(
                user_id=user_id,
                plan_id=new_plan_id,
                billing_cycle=billing_cycle
            )
        except Exception as e:
            logger.error(f"Error upgrading subscription: {str(e)}")
            return None

    @staticmethod
    def downgrade_subscription(user_id: str, new_plan_id: str) -> Optional[Subscription]:
        """Downgrade user's subscription to a new plan"""
        try:
            current_sub = SubscriptionService.get_subscription(user_id)
            if not current_sub:
                return None

            # Schedule downgrade for next billing cycle
            current_sub.plan = SubscriptionPlan.objects.get(id=new_plan_id)
            current_sub.updated_at = datetime.utcnow()
            return current_sub.save()
        except Exception as e:
            logger.error(f"Error downgrading subscription: {str(e)}")
            return None

    @staticmethod
    def cancel_subscription(user_id: str, immediate: bool = False) -> bool:
        """Cancel a user's subscription"""
        try:
            subscription = SubscriptionService.get_subscription(user_id)
            if not subscription:
                return False

            if immediate:
                subscription.status = SubscriptionStatus.CANCELED
                subscription.end_date = datetime.utcnow()
            else:
                # Cancel at end of billing period
                subscription.status = SubscriptionStatus.CANCELED

            subscription.canceled_at = datetime.utcnow()
            subscription.updated_at = datetime.utcnow()
            subscription.save()
            return True
        except Exception as e:
            logger.error(f"Error canceling subscription: {str(e)}")
            return False

    @staticmethod
    def get_all_subscriptions() -> List[Subscription]:
        """Get all subscriptions (admin function)"""
        try:
            return list(Subscription.objects.all().order_by('-created_at'))
        except Exception as e:
            logger.error(f"Error fetching all subscriptions: {str(e)}")
            return []

    # Custom Plan Methods
    @staticmethod
    def create_custom_plan(
        user_id: str,
        name: str,
        description: str,
        features: List[Dict]
    ) -> CustomPlan:
        """Create a custom plan for a user"""
        try:
            from ..models.subscription import CustomPlanFeature

            user = User.objects.get(id=user_id)

            custom_plan = CustomPlan(
                user=user,
                name=name,
                description=description,
                features=[],
                monthly_total=Decimal('0'),
                yearly_total=Decimal('0')
            )

            monthly_total = Decimal('0')
            yearly_total = Decimal('0')

            # Add features with pricing
            for feature_data in features:
                feature = Feature.objects(name=feature_data['feature_name']).first()
                if feature:
                    monthly_price = Decimal(str(feature_data.get('monthly_price', 0)))
                    yearly_price = Decimal(str(feature_data.get('yearly_price', 0)))

                    custom_feature = CustomPlanFeature(
                        feature=feature,
                        value=str(feature_data['value']),
                        monthly_price=monthly_price,
                        yearly_price=yearly_price
                    )
                    custom_plan.features.append(custom_feature)

                    monthly_total += monthly_price
                    yearly_total += yearly_price

            custom_plan.monthly_total = monthly_total
            custom_plan.yearly_total = yearly_total

            return custom_plan.save()
        except Exception as e:
            logger.error(f"Error creating custom plan: {str(e)}")
            raise

    @staticmethod
    def get_user_custom_plans(user_id: str) -> List[CustomPlan]:
        """Get all custom plans for a user"""
        try:
            user = User.objects.get(id=user_id)
            return list(CustomPlan.objects(user=user, is_active=True))
        except Exception as e:
            logger.error(f"Error fetching custom plans: {str(e)}")
            return []

    # Usage Tracking Methods
    @staticmethod
    def check_feature_limit(user_id: str, feature_name: str) -> Dict[str, Any]:
        """Check if user can use a feature based on their subscription limits"""
        try:
            subscription = SubscriptionService.get_subscription(user_id)
            if not subscription:
                return {"allowed": False, "reason": "No active subscription"}

            feature_value = subscription.get_feature_value(feature_name)
            if not feature_value:
                return {"allowed": False, "reason": "Feature not included in plan"}

            if feature_value == "unlimited":
                return {"allowed": True, "unlimited": True}

            try:
                limit = int(feature_value)
                current_usage = SubscriptionUsage.get_current_usage(
                    User.objects.get(id=user_id),
                    feature_name,
                    subscription
                )

                return {
                    "allowed": current_usage < limit,
                    "limit": limit,
                    "current_usage": current_usage,
                    "remaining": max(0, limit - current_usage)
                }
            except ValueError:
                # Boolean feature
                return {"allowed": feature_value.lower() == "true"}

        except Exception as e:
            logger.error(f"Error checking feature limit: {str(e)}")
            return {"allowed": False, "reason": "Error checking limits"}

    @staticmethod
    def increment_feature_usage(user_id: str, feature_name: str, count: int = 1) -> bool:
        """Increment usage for a feature"""
        try:
            user = User.objects.get(id=user_id)
            subscription = SubscriptionService.get_subscription(user_id)

            if not subscription:
                return False

            return SubscriptionUsage.increment_usage(user, feature_name, count, subscription)
        except Exception as e:
            logger.error(f"Error incrementing feature usage: {str(e)}")
            return False

    # Analytics Methods
    @staticmethod
    def get_subscription_analytics() -> Dict[str, Any]:
        """Get subscription analytics for admin dashboard"""
        try:
            total_subscriptions = Subscription.objects.count()
            active_subscriptions = Subscription.objects(status=SubscriptionStatus.ACTIVE).count()
            trial_subscriptions = Subscription.objects(status=SubscriptionStatus.TRIALING).count()
            canceled_subscriptions = Subscription.objects(status=SubscriptionStatus.CANCELED).count()

            # Revenue calculation (simplified)
            monthly_revenue = sum(
                float(sub.amount) for sub in Subscription.objects(
                    status=SubscriptionStatus.ACTIVE,
                    billing_cycle=BillingCycle.MONTHLY
                )
            )

            yearly_revenue = sum(
                float(sub.amount) for sub in Subscription.objects(
                    status=SubscriptionStatus.ACTIVE,
                    billing_cycle=BillingCycle.YEARLY
                )
            )

            return {
                "total_subscriptions": total_subscriptions,
                "active_subscriptions": active_subscriptions,
                "trial_subscriptions": trial_subscriptions,
                "canceled_subscriptions": canceled_subscriptions,
                "monthly_revenue": monthly_revenue,
                "yearly_revenue": yearly_revenue,
                "total_monthly_recurring_revenue": monthly_revenue + (yearly_revenue / 12)
            }
        except Exception as e:
            logger.error(f"Error getting subscription analytics: {str(e)}")
            return {}

    @staticmethod
    def get_user_usage_stats(user_id: str) -> Dict[str, Any]:
        """Get usage statistics for a user"""
        try:
            subscription = SubscriptionService.get_subscription(user_id)
            if not subscription:
                return {}

            user = User.objects.get(id=user_id)
            usage_stats = {}

            # Get usage for each feature in the plan
            if subscription.plan:
                for plan_feature in subscription.plan.features:
                    feature_name = plan_feature.feature.name
                    current_usage = SubscriptionUsage.get_current_usage(user, feature_name, subscription)
                    usage_percentage = subscription.get_usage_percentage(feature_name)

                    usage_stats[feature_name] = {
                        "current_usage": current_usage,
                        "limit": plan_feature.value,
                        "percentage": usage_percentage,
                        "unlimited": plan_feature.is_unlimited
                    }

            return usage_stats
        except Exception as e:
            logger.error(f"Error getting user usage stats: {str(e)}")
            return {}


