{% extends "base.html" %}

{% block title %}Subscription Plans - Rominext{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-5">
                <h1 class="display-4">Choose Your Plan</h1>
                <p class="lead text-muted">Select the perfect plan for your social media management needs</p>
                
                <!-- Billing Toggle -->
                <div class="d-flex justify-content-center align-items-center mb-4">
                    <span class="me-3">Monthly</span>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="billingToggle">
                        <label class="form-check-label" for="billingToggle"></label>
                    </div>
                    <span class="ms-3">Yearly <span class="badge bg-success">Save 20%</span></span>
                </div>
            </div>

            {% if plans %}
            <div class="row justify-content-center">
                {% for plan in plans %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm {% if current_subscription and current_subscription.plan and current_subscription.plan.id == plan.id %}border-primary{% endif %}">
                        {% if current_subscription and current_subscription.plan and current_subscription.plan.id == plan.id %}
                        <div class="card-header bg-primary text-white text-center">
                            <small><i class="fas fa-star me-1"></i>Current Plan</small>
                        </div>
                        {% endif %}
                        
                        <div class="card-body text-center">
                            <h4 class="card-title">{{ plan.display_name }}</h4>
                            <p class="text-muted">{{ plan.description }}</p>
                            
                            <!-- Pricing -->
                            <div class="pricing-section mb-4">
                                {% for tier in plan.pricing_tiers %}
                                <div class="price-tier {% if tier.billing_cycle.value == 'yearly' %}d-none{% endif %}" 
                                     data-cycle="{{ tier.billing_cycle.value }}">
                                    <h2 class="price">
                                        ${{ "%.0f"|format(tier.price) }}
                                        <small class="text-muted">/ {{ tier.billing_cycle.value }}</small>
                                    </h2>
                                    {% if tier.trial_days > 0 %}
                                    <p class="text-success small">
                                        <i class="fas fa-gift me-1"></i>{{ tier.trial_days }} day free trial
                                    </p>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                            
                            <!-- Features -->
                            <div class="features-list text-start mb-4">
                                {% for plan_feature in plan.features %}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <span>
                                        {% if plan_feature.is_unlimited %}
                                        Unlimited {{ plan_feature.feature.display_name }}
                                        {% else %}
                                        {{ plan_feature.value }} {{ plan_feature.feature.display_name }}
                                        {% if plan_feature.feature.unit %}({{ plan_feature.feature.unit }}){% endif %}
                                        {% endif %}
                                    </span>
                                </div>
                                {% endfor %}
                            </div>
                            
                            <!-- Action Button -->
                            {% if current_subscription and current_subscription.plan and current_subscription.plan.id == plan.id %}
                            <button class="btn btn-outline-primary w-100" disabled>
                                <i class="fas fa-check me-2"></i>Current Plan
                            </button>
                            {% else %}
                            <button class="btn btn-primary w-100" 
                                    onclick="selectPlan('{{ plan.id }}', '{{ plan.display_name }}')">
                                {% if current_subscription %}
                                Switch to {{ plan.display_name }}
                                {% else %}
                                Get Started
                                {% endif %}
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Custom Plan Option -->
            <div class="row justify-content-center mt-5">
                <div class="col-lg-8">
                    <div class="card border-dashed">
                        <div class="card-body text-center py-4">
                            <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                            <h5>Need Something Different?</h5>
                            <p class="text-muted">Create a custom plan tailored to your specific needs</p>
                            <a href="{{ url_for('user_subscription.custom_plan') }}" class="btn btn-outline-primary">
                                <i class="fas fa-plus me-2"></i>Build Custom Plan
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-muted mb-3"></i>
                <h4>No Plans Available</h4>
                <p class="text-muted">Please check back later or contact support.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Plan Selection Modal -->
<div class="modal fade" id="planModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Plan Selection</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <h6>You've selected: <strong id="selectedPlanName"></strong></h6>
                    <p class="text-muted">Choose your billing cycle:</p>
                </div>
                
                <div class="row" id="billingOptions">
                    <!-- Billing options will be populated by JavaScript -->
                </div>
                
                <div class="alert alert-info mt-3">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        {% if current_subscription %}
                        Your current subscription will be updated immediately.
                        {% else %}
                        You can cancel anytime. No long-term commitments.
                        {% endif %}
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmPlanSelection()" id="confirmBtn">
                    {% if current_subscription %}Update Plan{% else %}Subscribe Now{% endif %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-dashed {
    border: 2px dashed #dee2e6 !important;
}

.pricing-section {
    min-height: 80px;
}

.price {
    color: #007bff;
    font-weight: bold;
}

.features-list {
    min-height: 200px;
}

.card:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

.billing-option {
    cursor: pointer;
    transition: all 0.3s ease;
}

.billing-option:hover {
    background-color: #f8f9fa;
}

.billing-option.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let selectedPlanId = null;
let selectedPlanData = null;
let selectedBillingCycle = 'monthly';

// Toggle between monthly and yearly pricing
document.getElementById('billingToggle').addEventListener('change', function() {
    const isYearly = this.checked;
    
    document.querySelectorAll('.price-tier').forEach(tier => {
        const cycle = tier.getAttribute('data-cycle');
        if ((isYearly && cycle === 'yearly') || (!isYearly && cycle === 'monthly')) {
            tier.classList.remove('d-none');
        } else {
            tier.classList.add('d-none');
        }
    });
});

function selectPlan(planId, planName) {
    selectedPlanId = planId;
    selectedPlanData = {{ plans|tojson }}.find(p => p.id === planId);
    
    document.getElementById('selectedPlanName').textContent = planName;
    
    // Populate billing options
    const billingOptions = document.getElementById('billingOptions');
    billingOptions.innerHTML = '';
    
    selectedPlanData.pricing_tiers.forEach(tier => {
        const isYearly = tier.billing_cycle.value === 'yearly';
        const savings = isYearly ? ' (Save 20%)' : '';
        
        billingOptions.innerHTML += `
            <div class="col-6">
                <div class="card billing-option" onclick="selectBilling('${tier.billing_cycle.value}')">
                    <div class="card-body text-center p-3">
                        <h6>${tier.billing_cycle.value.charAt(0).toUpperCase() + tier.billing_cycle.value.slice(1)}</h6>
                        <h5 class="text-primary">$${tier.price.toFixed(2)}</h5>
                        <small class="text-muted">per ${tier.billing_cycle.value}${savings}</small>
                        ${tier.trial_days > 0 ? `<div class="text-success small mt-1">${tier.trial_days} day trial</div>` : ''}
                    </div>
                </div>
            </div>
        `;
    });
    
    // Select monthly by default
    selectBilling('monthly');
    
    new bootstrap.Modal(document.getElementById('planModal')).show();
}

function selectBilling(cycle) {
    selectedBillingCycle = cycle;
    
    // Update visual selection
    document.querySelectorAll('.billing-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    event.currentTarget.classList.add('selected');
}

function confirmPlanSelection() {
    const confirmBtn = document.getElementById('confirmBtn');
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    
    const endpoint = {{ 'true' if current_subscription else 'false' }} ? '/subscription/upgrade' : '/subscription/subscribe';
    
    fetch(endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            plan_id: selectedPlanId,
            billing_cycle: selectedBillingCycle
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            window.location.href = '/subscription/';
        } else {
            alert('Error: ' + data.error);
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '{{ "Update Plan" if current_subscription else "Subscribe Now" }}';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while processing your request');
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = '{{ "Update Plan" if current_subscription else "Subscribe Now" }}';
    });
}
</script>
{% endblock %}
