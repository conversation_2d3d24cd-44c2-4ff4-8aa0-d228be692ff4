{% extends "admin/admin_layout.html" %}

{% block title %}پلن‌های اشتراک - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-list me-2"></i>
                    پلن‌های اشتراک
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('admin.subscription_plan_create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>ایجاد پلن جدید
                    </a>
                    <a href="{{ url_for('admin.subscription_features') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cogs me-2"></i>مدیریت امکانات
                    </a>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-body">
                    {% if plans %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>نام پلن</th>
                                    <th>نام نمایشی</th>
                                    <th>قیمت‌گذاری</th>
                                    <th>امکانات</th>
                                    <th>نمایش</th>
                                    <th>وضعیت</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for plan in plans %}
                                <tr>
                                    <td>
                                        <strong>{{ plan.name }}</strong>
                                    </td>
                                    <td>{{ plan.display_name }}</td>
                                    <td>
                                        {% for tier in plan.pricing_tiers %}
                                        <div class="mb-1">
                                            <span class="badge bg-info">{{ tier.billing_cycle.value.title() }}</span>
                                            ${{ "%.2f"|format(tier.price) }}
                                            {% if tier.trial_days > 0 %}
                                            <small class="text-muted">({{ tier.trial_days }} روز آزمایشی)</small>
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ plan.features|length }} امکانات</span>
                                    </td>
                                    <td>
                                        {% if plan.visibility.value == 'public' %}
                                        <span class="badge bg-success">عمومی</span>
                                        {% elif plan.visibility.value == 'private' %}
                                        <span class="badge bg-warning">خصوصی</span>
                                        {% else %}
                                        <span class="badge bg-danger">آرشیو شده</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if plan.is_active %}
                                        <span class="badge bg-success">فعال</span>
                                        {% else %}
                                        <span class="badge bg-danger">غیرفعال</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('admin.subscription_plan_edit', plan_id=plan.id) }}"
                                               class="btn btn-outline-primary" title="ویرایش">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="confirmDelete('{{ plan.id }}', '{{ plan.display_name }}')"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-list fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">هیچ پلن اشتراکی یافت نشد</h5>
                        <p class="text-muted">اولین پلن اشتراک خود را برای شروع ایجاد کنید.</p>
                        <a href="{{ url_for('admin.subscription_plan_create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>ایجاد اولین پلن
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأیید حذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>آیا مطمئن هستید که می‌خواهید پلن "<span id="planName"></span>" را حذف کنید؟</p>
                <p class="text-muted small">این عمل پلن را آرشیو کرده و برای اشتراک‌های جدید در دسترس نخواهد بود.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">لغو</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف پلن</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(planId, planName) {
    document.getElementById('planName').textContent = planName;
    document.getElementById('deleteForm').action = `/admin/subscriptions/plans/${planId}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
