from mongoengine import (
    Document, ReferenceField, StringField, DateTimeField, IntField,
    EnumField, DecimalField, BooleanField, ListField, DictField, EmbeddedDocument, EmbeddedDocumentField
)
from datetime import datetime, timedelta
from enum import Enum
from .user import User

class SubscriptionStatus(str, Enum):
    ACTIVE = "active"
    EXPIRED = "expired"
    CANCELED = "canceled"
    TRIALING = "trialing"
    PAST_DUE = "past_due"
    SUSPENDED = "suspended"

class BillingCycle(str, Enum):
    MONTHLY = "monthly"
    YEARLY = "yearly"
    QUARTERLY = "quarterly"
    WEEKLY = "weekly"

class FeatureType(str, Enum):
    LIMIT = "limit"  # Numeric limit (e.g., 10 posts/day)
    BOOLEAN = "boolean"  # True/False feature (e.g., priority support)
    QUOTA = "quota"  # Usage-based quota (e.g., 1000 API calls/month)

class PlanVisibility(str, Enum):
    PUBLIC = "public"
    PRIVATE = "private"
    ARCHIVED = "archived"

class PricingTier(EmbeddedDocument):
    """Embedded document for different pricing tiers (monthly, yearly, etc.)"""
    billing_cycle = EnumField(BillingCycle, required=True)
    price = DecimalField(required=True, precision=2, min_value=0)
    currency = StringField(default="USD", max_length=3)
    trial_days = IntField(default=0, min_value=0)
    setup_fee = DecimalField(default=0, precision=2, min_value=0)

class Feature(Document):
    """Individual features that can be assigned to plans"""
    name = StringField(required=True, unique=True, max_length=100)
    display_name = StringField(required=True, max_length=200)
    description = StringField(max_length=500)
    feature_type = EnumField(FeatureType, required=True)
    default_value = StringField()  # Store as string, convert based on type
    unit = StringField(max_length=50)  # e.g., "posts", "users", "GB"
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'features',
        'indexes': ['name', 'is_active']
    }

class PlanFeature(EmbeddedDocument):
    """Features assigned to a specific plan with their values"""
    feature = ReferenceField(Feature, required=True)
    value = StringField(required=True)  # Store as string, convert based on feature type
    is_unlimited = BooleanField(default=False)

class SubscriptionPlan(Document):
    """Subscription plans that users can subscribe to"""
    name = StringField(required=True, unique=True, max_length=100)
    display_name = StringField(required=True, max_length=200)
    description = StringField(max_length=1000)

    # Pricing tiers for different billing cycles
    pricing_tiers = ListField(EmbeddedDocumentField(PricingTier))

    # Features included in this plan
    features = ListField(EmbeddedDocumentField(PlanFeature))

    # Plan settings
    visibility = EnumField(PlanVisibility, default=PlanVisibility.PUBLIC)
    is_active = BooleanField(default=True)
    is_custom_plan_base = BooleanField(default=False)  # Can be used as base for custom plans
    sort_order = IntField(default=0)  # For ordering plans in UI

    # Metadata
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'subscription_plans',
        'indexes': ['name', 'is_active', 'visibility', 'sort_order']
    }

class CustomPlanFeature(EmbeddedDocument):
    """Features in a custom plan with individual pricing"""
    feature = ReferenceField(Feature, required=True)
    value = StringField(required=True)
    monthly_price = DecimalField(precision=2, min_value=0)
    yearly_price = DecimalField(precision=2, min_value=0)

class CustomPlan(Document):
    """Custom plans created by users"""
    user = ReferenceField(User, required=True)
    name = StringField(required=True, max_length=200)
    description = StringField(max_length=1000)

    # Custom features with individual pricing
    features = ListField(EmbeddedDocumentField(CustomPlanFeature))

    # Calculated totals
    monthly_total = DecimalField(precision=2, min_value=0)
    yearly_total = DecimalField(precision=2, min_value=0)

    # Plan status
    is_active = BooleanField(default=True)
    is_subscribed = BooleanField(default=False)

    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'custom_plans',
        'indexes': ['user', 'is_active']
    }

class Subscription(Document):
    user = ReferenceField(User, required=True)

    # Plan references
    plan = ReferenceField(SubscriptionPlan)  # Standard plan
    custom_plan = ReferenceField(CustomPlan)  # Custom plan
    plan_name = StringField(required=True)  # Keep for backward compatibility

    # Subscription details
    status = EnumField(SubscriptionStatus, default=SubscriptionStatus.ACTIVE)
    billing_cycle = EnumField(BillingCycle, default=BillingCycle.MONTHLY)

    # Dates
    start_date = DateTimeField(required=True)
    end_date = DateTimeField(required=True)
    trial_end_date = DateTimeField()
    next_billing_date = DateTimeField()
    canceled_at = DateTimeField()

    # Pricing
    amount = DecimalField(precision=2, min_value=0)
    currency = StringField(default="USD", max_length=3)

    # Legacy limits (keep for backward compatibility)
    posts_limit_per_month = IntField(default=100)
    accounts_limit = IntField(default=5)
    agents_limit = IntField(default=3)

    # Usage tracking
    current_usage = DictField(default=dict)  # Track current period usage

    # Payment integration
    stripe_subscription_id = StringField()
    paypal_subscription_id = StringField()

    # Metadata
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'subscriptions',
        'indexes': ['user', 'status', 'plan', 'next_billing_date']
    }

    @property
    def is_trial(self):
        """Check if subscription is in trial period"""
        if self.trial_end_date and self.trial_end_date > datetime.utcnow():
            return True
        return False

    @property
    def days_until_renewal(self):
        """Get days until next billing"""
        if self.next_billing_date:
            delta = self.next_billing_date - datetime.utcnow()
            return max(0, delta.days)
        return 0

    def get_feature_value(self, feature_name):
        """Get the value of a specific feature for this subscription"""
        if self.plan:
            for plan_feature in self.plan.features:
                if plan_feature.feature.name == feature_name:
                    if plan_feature.is_unlimited:
                        return "unlimited"
                    return plan_feature.value
        elif self.custom_plan:
            for custom_feature in self.custom_plan.features:
                if custom_feature.feature.name == feature_name:
                    return custom_feature.value
        return None

    def get_usage_percentage(self, feature_name):
        """Get usage percentage for a quota-based feature"""
        feature_value = self.get_feature_value(feature_name)
        if not feature_value or feature_value == "unlimited":
            return 0

        try:
            limit = int(feature_value)
            current = self.current_usage.get(feature_name, 0)
            return min(100, (current / limit) * 100) if limit > 0 else 0
        except (ValueError, TypeError):
            return 0
