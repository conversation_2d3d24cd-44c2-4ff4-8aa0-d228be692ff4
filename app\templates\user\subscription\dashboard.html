{% extends "base.html" %}

{% block title %}My Subscription - Rominext{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-credit-card me-2"></i>
                    My Subscription
                </h2>
                {% if not current_subscription %}
                <a href="{{ url_for('user_subscription.plans') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Choose a Plan
                </a>
                {% endif %}
            </div>

            {% if current_subscription %}
            <!-- Current Subscription Card -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-star me-2"></i>
                                Current Plan: {{ current_subscription.plan_name }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Plan Details</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                {% if current_subscription.status.value == 'active' %}
                                                <span class="badge bg-success">Active</span>
                                                {% elif current_subscription.status.value == 'trialing' %}
                                                <span class="badge bg-info">Trial</span>
                                                {% else %}
                                                <span class="badge bg-warning">{{ current_subscription.status.value.title() }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Billing:</strong></td>
                                            <td>{{ current_subscription.billing_cycle.value.title() }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Amount:</strong></td>
                                            <td>${{ "%.2f"|format(current_subscription.amount) }} {{ current_subscription.currency }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Next Billing:</strong></td>
                                            <td>
                                                {% if current_subscription.next_billing_date %}
                                                {{ current_subscription.next_billing_date.strftime('%B %d, %Y') }}
                                                {% else %}
                                                -
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>Actions</h6>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('user_subscription.plans') }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-arrow-up me-2"></i>Upgrade Plan
                                        </a>
                                        <button class="btn btn-outline-warning btn-sm" onclick="cancelSubscription()">
                                            <i class="fas fa-ban me-2"></i>Cancel Subscription
                                        </button>
                                        <a href="{{ url_for('user_subscription.usage') }}" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-chart-bar me-2"></i>View Usage
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            {% if current_subscription.is_trial %}
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-clock me-2"></i>
                                <strong>Trial Period:</strong> Your trial ends on {{ current_subscription.trial_end_date.strftime('%B %d, %Y') }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Quick Stats</h6>
                        </div>
                        <div class="card-body">
                            {% if usage_stats %}
                            {% for feature_name, stats in usage_stats.items() %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small class="text-muted">{{ feature_name.replace('_', ' ').title() }}</small>
                                    <small class="text-muted">
                                        {% if stats.unlimited %}
                                        Unlimited
                                        {% else %}
                                        {{ stats.current_usage }}/{{ stats.limit }}
                                        {% endif %}
                                    </small>
                                </div>
                                {% if not stats.unlimited %}
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar 
                                        {% if stats.percentage > 80 %}bg-danger
                                        {% elif stats.percentage > 60 %}bg-warning
                                        {% else %}bg-success{% endif %}" 
                                        style="width: {{ stats.percentage }}%"></div>
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                            {% else %}
                            <p class="text-muted text-center">No usage data available</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available Upgrades -->
            {% if available_plans %}
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-arrow-up me-2"></i>
                        Available Upgrades
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for plan in available_plans %}
                        {% if plan.id != current_subscription.plan.id %}
                        <div class="col-md-4 mb-3">
                            <div class="card border">
                                <div class="card-body">
                                    <h6 class="card-title">{{ plan.display_name }}</h6>
                                    <p class="card-text text-muted small">{{ plan.description[:100] }}...</p>
                                    <div class="mb-2">
                                        {% for tier in plan.pricing_tiers %}
                                        <div class="d-flex justify-content-between">
                                            <span class="small">{{ tier.billing_cycle.value.title() }}:</span>
                                            <strong>${{ "%.2f"|format(tier.price) }}</strong>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm w-100"
                                            onclick="upgradeToPlan('{{ plan.id }}', '{{ plan.display_name }}')">
                                        Upgrade to {{ plan.display_name }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            {% else %}
            <!-- No Subscription -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm text-center">
                        <div class="card-body py-5">
                            <i class="fas fa-credit-card fa-4x text-muted mb-4"></i>
                            <h4>No Active Subscription</h4>
                            <p class="text-muted mb-4">
                                Choose a subscription plan to unlock all features and start managing your social media like a pro.
                            </p>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <a href="{{ url_for('user_subscription.plans') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-eye me-2"></i>View Plans
                                </a>
                                <a href="{{ url_for('user_subscription.custom_plan') }}" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-cogs me-2"></i>Create Custom Plan
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Cancel Subscription Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel your subscription?</p>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="immediateCancel">
                    <label class="form-check-label" for="immediateCancel">
                        Cancel immediately (you will lose access right away)
                    </label>
                </div>
                <p class="text-muted small mt-2">
                    If not checked, your subscription will remain active until the end of your current billing period.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Subscription</button>
                <button type="button" class="btn btn-danger" onclick="confirmCancel()">Cancel Subscription</button>
            </div>
        </div>
    </div>
</div>

<!-- Upgrade Modal -->
<div class="modal fade" id="upgradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upgrade Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Upgrade to <strong id="upgradePlanName"></strong>?</p>
                <div class="mb-3">
                    <label class="form-label">Billing Cycle</label>
                    <select class="form-select" id="upgradeBillingCycle">
                        <option value="monthly">Monthly</option>
                        <option value="yearly">Yearly</option>
                    </select>
                </div>
                <p class="text-muted small">
                    Your subscription will be upgraded immediately and you'll be charged the prorated amount.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmUpgrade()">Upgrade Now</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedPlanId = null;

function cancelSubscription() {
    new bootstrap.Modal(document.getElementById('cancelModal')).show();
}

function confirmCancel() {
    const immediate = document.getElementById('immediateCancel').checked;
    
    fetch('/subscription/cancel', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ immediate: immediate })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while canceling subscription');
    });
    
    bootstrap.Modal.getInstance(document.getElementById('cancelModal')).hide();
}

function upgradeToPlan(planId, planName) {
    selectedPlanId = planId;
    document.getElementById('upgradePlanName').textContent = planName;
    new bootstrap.Modal(document.getElementById('upgradeModal')).show();
}

function confirmUpgrade() {
    const billingCycle = document.getElementById('upgradeBillingCycle').value;
    
    fetch('/subscription/upgrade', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
            plan_id: selectedPlanId,
            billing_cycle: billingCycle
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while upgrading subscription');
    });
    
    bootstrap.Modal.getInstance(document.getElementById('upgradeModal')).hide();
}
</script>
{% endblock %}
