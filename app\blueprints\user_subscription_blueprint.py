from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from marshmallow import Schema, fields, ValidationError
from datetime import datetime
import logging
from ..services.subscription_service import SubscriptionService
from ..models.subscription import (
    SubscriptionPlan, CustomPlan, Feature, BillingCycle, 
    FeatureType, PlanVisibility
)
from ..models.user import User

logger = logging.getLogger(__name__)
user_subscription_bp = Blueprint('user_subscription', __name__, url_prefix='/subscription')

# Validation schemas
class SubscribeSchema(Schema):
    plan_id = fields.String(required=True)
    billing_cycle = fields.String(required=True, validate=lambda x: x in ['monthly', 'yearly'])

class CustomPlanSchema(Schema):
    name = fields.String(required=True, validate=lambda x: len(x.strip()) > 0)
    description = fields.String(missing="")
    features = fields.List(fields.Dict(), required=True)

@user_subscription_bp.route('/')
@login_required
def index():
    """User subscription dashboard"""
    try:
        # Get current subscription
        current_subscription = SubscriptionService.get_subscription(str(current_user.id))
        
        # Get usage stats if user has subscription
        usage_stats = {}
        if current_subscription:
            usage_stats = SubscriptionService.get_user_usage_stats(str(current_user.id))
        
        # Get available plans for upgrade/downgrade
        available_plans = SubscriptionService.get_public_plans()
        
        return render_template('user/subscription/dashboard.html',
                             current_subscription=current_subscription,
                             usage_stats=usage_stats,
                             available_plans=available_plans)
    except Exception as e:
        logger.error(f"Error loading subscription dashboard: {str(e)}")
        flash('Error loading subscription information', 'danger')
        return redirect(url_for('dashboard.index'))

@user_subscription_bp.route('/plans')
@login_required
def plans():
    """View available subscription plans"""
    try:
        plans = SubscriptionService.get_public_plans()
        current_subscription = SubscriptionService.get_subscription(str(current_user.id))
        
        return render_template('user/subscription/plans.html',
                             plans=plans,
                             current_subscription=current_subscription)
    except Exception as e:
        logger.error(f"Error loading plans: {str(e)}")
        flash('Error loading subscription plans', 'danger')
        return redirect(url_for('user_subscription.index'))

@user_subscription_bp.route('/subscribe', methods=['POST'])
@login_required
def subscribe():
    """Subscribe to a plan"""
    try:
        schema = SubscribeSchema()
        data = schema.load(request.json)
        
        # Create subscription
        subscription = SubscriptionService.create_subscription(
            user_id=str(current_user.id),
            plan_id=data['plan_id'],
            billing_cycle=data['billing_cycle']
        )
        
        if subscription:
            return jsonify({
                "success": True,
                "message": "Successfully subscribed to plan!",
                "subscription_id": str(subscription.id)
            }), 201
        else:
            return jsonify({
                "success": False,
                "error": "Failed to create subscription"
            }), 500
            
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error subscribing to plan: {str(e)}")
        return jsonify({"success": False, "error": "Failed to subscribe"}), 500

@user_subscription_bp.route('/upgrade', methods=['POST'])
@login_required
def upgrade():
    """Upgrade subscription"""
    try:
        plan_id = request.json.get('plan_id')
        billing_cycle = request.json.get('billing_cycle', 'monthly')
        
        if not plan_id:
            return jsonify({"success": False, "error": "Plan ID required"}), 400
        
        subscription = SubscriptionService.upgrade_subscription(
            user_id=str(current_user.id),
            new_plan_id=plan_id,
            billing_cycle=billing_cycle
        )
        
        if subscription:
            return jsonify({
                "success": True,
                "message": "Successfully upgraded subscription!"
            }), 200
        else:
            return jsonify({
                "success": False,
                "error": "Failed to upgrade subscription"
            }), 500
            
    except Exception as e:
        logger.error(f"Error upgrading subscription: {str(e)}")
        return jsonify({"success": False, "error": "Failed to upgrade"}), 500

@user_subscription_bp.route('/downgrade', methods=['POST'])
@login_required
def downgrade():
    """Downgrade subscription"""
    try:
        plan_id = request.json.get('plan_id')
        
        if not plan_id:
            return jsonify({"success": False, "error": "Plan ID required"}), 400
        
        subscription = SubscriptionService.downgrade_subscription(
            user_id=str(current_user.id),
            new_plan_id=plan_id
        )
        
        if subscription:
            return jsonify({
                "success": True,
                "message": "Subscription will be downgraded at next billing cycle"
            }), 200
        else:
            return jsonify({
                "success": False,
                "error": "Failed to schedule downgrade"
            }), 500
            
    except Exception as e:
        logger.error(f"Error downgrading subscription: {str(e)}")
        return jsonify({"success": False, "error": "Failed to downgrade"}), 500

@user_subscription_bp.route('/cancel', methods=['POST'])
@login_required
def cancel():
    """Cancel subscription"""
    try:
        immediate = request.json.get('immediate', False)
        
        success = SubscriptionService.cancel_subscription(
            user_id=str(current_user.id),
            immediate=immediate
        )
        
        if success:
            message = "Subscription canceled immediately" if immediate else "Subscription will be canceled at end of billing period"
            return jsonify({
                "success": True,
                "message": message
            }), 200
        else:
            return jsonify({
                "success": False,
                "error": "Failed to cancel subscription"
            }), 500
            
    except Exception as e:
        logger.error(f"Error canceling subscription: {str(e)}")
        return jsonify({"success": False, "error": "Failed to cancel"}), 500

@user_subscription_bp.route('/custom-plan')
@login_required
def custom_plan():
    """Custom plan builder"""
    try:
        features = SubscriptionService.get_all_features()
        user_custom_plans = SubscriptionService.get_user_custom_plans(str(current_user.id))
        
        return render_template('user/subscription/custom_plan.html',
                             features=features,
                             custom_plans=user_custom_plans)
    except Exception as e:
        logger.error(f"Error loading custom plan builder: {str(e)}")
        flash('Error loading custom plan builder', 'danger')
        return redirect(url_for('user_subscription.index'))

@user_subscription_bp.route('/custom-plan/create', methods=['POST'])
@login_required
def create_custom_plan():
    """Create a custom plan"""
    try:
        schema = CustomPlanSchema()
        data = schema.load(request.json)
        
        custom_plan = SubscriptionService.create_custom_plan(
            user_id=str(current_user.id),
            name=data['name'],
            description=data['description'],
            features=data['features']
        )
        
        return jsonify({
            "success": True,
            "message": "Custom plan created successfully!",
            "plan_id": str(custom_plan.id),
            "monthly_total": float(custom_plan.monthly_total),
            "yearly_total": float(custom_plan.yearly_total)
        }), 201
        
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error creating custom plan: {str(e)}")
        return jsonify({"success": False, "error": "Failed to create custom plan"}), 500

@user_subscription_bp.route('/custom-plan/<plan_id>/subscribe', methods=['POST'])
@login_required
def subscribe_custom_plan(plan_id):
    """Subscribe to a custom plan"""
    try:
        billing_cycle = request.json.get('billing_cycle', 'monthly')
        
        subscription = SubscriptionService.create_subscription(
            user_id=str(current_user.id),
            custom_plan_id=plan_id,
            billing_cycle=billing_cycle
        )
        
        if subscription:
            # Mark custom plan as subscribed
            custom_plan = CustomPlan.objects.get(id=plan_id)
            custom_plan.is_subscribed = True
            custom_plan.save()
            
            return jsonify({
                "success": True,
                "message": "Successfully subscribed to custom plan!",
                "subscription_id": str(subscription.id)
            }), 201
        else:
            return jsonify({
                "success": False,
                "error": "Failed to subscribe to custom plan"
            }), 500
            
    except Exception as e:
        logger.error(f"Error subscribing to custom plan: {str(e)}")
        return jsonify({"success": False, "error": "Failed to subscribe"}), 500

@user_subscription_bp.route('/usage')
@login_required
def usage():
    """View usage statistics"""
    try:
        current_subscription = SubscriptionService.get_subscription(str(current_user.id))
        if not current_subscription:
            flash('No active subscription found', 'warning')
            return redirect(url_for('user_subscription.plans'))
        
        usage_stats = SubscriptionService.get_user_usage_stats(str(current_user.id))
        
        return render_template('user/subscription/usage.html',
                             subscription=current_subscription,
                             usage_stats=usage_stats)
    except Exception as e:
        logger.error(f"Error loading usage stats: {str(e)}")
        flash('Error loading usage statistics', 'danger')
        return redirect(url_for('user_subscription.index'))

@user_subscription_bp.route('/api/calculate-custom-price', methods=['POST'])
@login_required
def calculate_custom_price():
    """Calculate price for custom plan features"""
    try:
        features = request.json.get('features', [])
        
        monthly_total = 0
        yearly_total = 0
        
        # In a real implementation, you would have pricing rules for features
        # For now, we'll use simple placeholder pricing
        for feature_data in features:
            feature = Feature.objects(name=feature_data['feature_name']).first()
            if feature:
                # Placeholder pricing logic
                base_price = 5.0  # $5 per feature base price
                value = int(feature_data.get('value', 1))
                
                if feature.feature_type.value == 'limit':
                    monthly_price = base_price * (value / 100)  # Scale by value
                elif feature.feature_type.value == 'boolean':
                    monthly_price = base_price if feature_data.get('value', 'false').lower() == 'true' else 0
                else:  # quota
                    monthly_price = base_price * (value / 1000)
                
                yearly_price = monthly_price * 10  # 2 months free for yearly
                
                monthly_total += monthly_price
                yearly_total += yearly_price
        
        return jsonify({
            "success": True,
            "monthly_total": round(monthly_total, 2),
            "yearly_total": round(yearly_total, 2)
        }), 200
        
    except Exception as e:
        logger.error(f"Error calculating custom price: {str(e)}")
        return jsonify({"success": False, "error": "Failed to calculate price"}), 500
