{% extends "base.html" %}

{% block title %}Custom Plan Builder - Rominext{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-cogs me-2"></i>
                        Custom Plan Builder
                    </h2>
                    <p class="text-muted">Create a plan tailored to your specific needs</p>
                </div>
                <a href="{{ url_for('user_subscription.plans') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Plans
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Plan Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="planName" class="form-label">Plan Name *</label>
                                    <input type="text" class="form-control" id="planName" placeholder="My Custom Plan">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="planDescription" class="form-label">Description</label>
                                    <input type="text" class="form-control" id="planDescription" placeholder="Plan description">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card shadow-sm mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Select Features</h5>
                        </div>
                        <div class="card-body">
                            {% if features %}
                            <div class="row">
                                {% for feature in features %}
                                <div class="col-md-6 mb-4">
                                    <div class="card border feature-card" data-feature="{{ feature.name }}">
                                        <div class="card-body">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input feature-checkbox" type="checkbox" 
                                                       id="feature_{{ feature.id }}" data-feature-name="{{ feature.name }}">
                                                <label class="form-check-label fw-bold" for="feature_{{ feature.id }}">
                                                    {{ feature.display_name }}
                                                </label>
                                            </div>
                                            <p class="text-muted small mb-3">{{ feature.description }}</p>
                                            
                                            {% if feature.feature_type.value == 'boolean' %}
                                            <div class="feature-value d-none">
                                                <select class="form-select form-select-sm" data-feature="{{ feature.name }}">
                                                    <option value="true">Enabled</option>
                                                    <option value="false">Disabled</option>
                                                </select>
                                            </div>
                                            {% else %}
                                            <div class="feature-value d-none">
                                                <div class="input-group input-group-sm">
                                                    <input type="number" class="form-control" 
                                                           placeholder="Enter value" min="1" 
                                                           data-feature="{{ feature.name }}">
                                                    {% if feature.unit %}
                                                    <span class="input-group-text">{{ feature.unit }}</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            {% endif %}
                                            
                                            <div class="feature-pricing d-none mt-2">
                                                <small class="text-muted">
                                                    Estimated: <span class="monthly-price">$0.00</span>/month, 
                                                    <span class="yearly-price">$0.00</span>/year
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-exclamation-triangle fa-2x text-muted mb-3"></i>
                                <h6>No Features Available</h6>
                                <p class="text-muted">Please contact support to set up custom features.</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card shadow-sm position-sticky" style="top: 20px;">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Plan Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6>Selected Features</h6>
                                <div id="selectedFeatures">
                                    <p class="text-muted small">No features selected</p>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="pricing-summary">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Monthly Total:</span>
                                    <strong id="monthlyTotal">$0.00</strong>
                                </div>
                                <div class="d-flex justify-content-between mb-3">
                                    <span>Yearly Total:</span>
                                    <strong id="yearlyTotal">$0.00</strong>
                                </div>
                                <small class="text-success">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Yearly billing saves you money!
                                </small>
                            </div>
                            
                            <hr>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="createCustomPlan()" id="createPlanBtn" disabled>
                                    <i class="fas fa-save me-2"></i>Create Plan
                                </button>
                                <button class="btn btn-outline-success" onclick="subscribeToCustomPlan()" id="subscribeBtn" disabled>
                                    <i class="fas fa-credit-card me-2"></i>Create & Subscribe
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Existing Custom Plans -->
            {% if custom_plans %}
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Your Custom Plans</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% for plan in custom_plans %}
                                <div class="col-md-4 mb-3">
                                    <div class="card border">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ plan.name }}</h6>
                                            <p class="card-text text-muted small">{{ plan.description }}</p>
                                            <div class="mb-2">
                                                <small class="text-muted">
                                                    Monthly: <strong>${{ "%.2f"|format(plan.monthly_total) }}</strong><br>
                                                    Yearly: <strong>${{ "%.2f"|format(plan.yearly_total) }}</strong>
                                                </small>
                                            </div>
                                            {% if not plan.is_subscribed %}
                                            <button class="btn btn-outline-primary btn-sm w-100" 
                                                    onclick="subscribeToExistingPlan('{{ plan.id }}')">
                                                Subscribe to This Plan
                                            </button>
                                            {% else %}
                                            <span class="badge bg-success w-100">Active</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.feature-card {
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.feature-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.pricing-summary {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let selectedFeatures = [];
let currentPlanId = null;

// Feature selection handling
document.querySelectorAll('.feature-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const featureCard = this.closest('.feature-card');
        const featureValue = featureCard.querySelector('.feature-value');
        const featurePricing = featureCard.querySelector('.feature-pricing');
        
        if (this.checked) {
            featureCard.classList.add('selected');
            featureValue.classList.remove('d-none');
            featurePricing.classList.remove('d-none');
        } else {
            featureCard.classList.remove('selected');
            featureValue.classList.add('d-none');
            featurePricing.classList.add('d-none');
        }
        
        updatePlanSummary();
    });
});

// Value change handling
document.querySelectorAll('.feature-value input, .feature-value select').forEach(input => {
    input.addEventListener('input', updatePlanSummary);
});

function updatePlanSummary() {
    selectedFeatures = [];
    let monthlyTotal = 0;
    let yearlyTotal = 0;
    
    document.querySelectorAll('.feature-checkbox:checked').forEach(checkbox => {
        const featureName = checkbox.getAttribute('data-feature-name');
        const featureCard = checkbox.closest('.feature-card');
        const valueInput = featureCard.querySelector('.feature-value input, .feature-value select');
        const value = valueInput ? valueInput.value : '1';
        
        selectedFeatures.push({
            feature_name: featureName,
            value: value
        });
    });
    
    // Calculate pricing
    if (selectedFeatures.length > 0) {
        fetch('/subscription/api/calculate-custom-price', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ features: selectedFeatures })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                monthlyTotal = data.monthly_total;
                yearlyTotal = data.yearly_total;
                
                document.getElementById('monthlyTotal').textContent = `$${monthlyTotal.toFixed(2)}`;
                document.getElementById('yearlyTotal').textContent = `$${yearlyTotal.toFixed(2)}`;
            }
        })
        .catch(error => console.error('Error calculating price:', error));
    } else {
        document.getElementById('monthlyTotal').textContent = '$0.00';
        document.getElementById('yearlyTotal').textContent = '$0.00';
    }
    
    // Update selected features display
    const selectedFeaturesDiv = document.getElementById('selectedFeatures');
    if (selectedFeatures.length > 0) {
        selectedFeaturesDiv.innerHTML = selectedFeatures.map(f => 
            `<small class="badge bg-light text-dark me-1 mb-1">${f.feature_name}: ${f.value}</small>`
        ).join('');
    } else {
        selectedFeaturesDiv.innerHTML = '<p class="text-muted small">No features selected</p>';
    }
    
    // Enable/disable buttons
    const hasFeatures = selectedFeatures.length > 0;
    const hasName = document.getElementById('planName').value.trim().length > 0;
    
    document.getElementById('createPlanBtn').disabled = !(hasFeatures && hasName);
    document.getElementById('subscribeBtn').disabled = !(hasFeatures && hasName);
}

// Plan name change handling
document.getElementById('planName').addEventListener('input', updatePlanSummary);

function createCustomPlan() {
    const planName = document.getElementById('planName').value.trim();
    const planDescription = document.getElementById('planDescription').value.trim();
    
    if (!planName || selectedFeatures.length === 0) {
        alert('Please enter a plan name and select at least one feature.');
        return;
    }
    
    const createBtn = document.getElementById('createPlanBtn');
    createBtn.disabled = true;
    createBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    
    fetch('/subscription/custom-plan/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: planName,
            description: planDescription,
            features: selectedFeatures
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Custom plan created successfully!');
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Failed to create plan'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while creating the plan');
    })
    .finally(() => {
        createBtn.disabled = false;
        createBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Plan';
    });
}

function subscribeToCustomPlan() {
    // First create the plan, then subscribe
    createCustomPlan();
}

function subscribeToExistingPlan(planId) {
    if (confirm('Subscribe to this custom plan?')) {
        fetch(`/subscription/custom-plan/${planId}/subscribe`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ billing_cycle: 'monthly' })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Successfully subscribed to custom plan!');
                window.location.href = '/subscription/';
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while subscribing');
        });
    }
}
</script>
{% endblock %}
