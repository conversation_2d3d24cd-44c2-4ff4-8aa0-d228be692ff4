{% extends "admin/admin_layout.html" %}

{% block title %}Subscription Analytics - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-chart-bar me-2"></i>
                    Subscription Analytics
                </h2>
                <div class="btn-group">
                    <button class="btn btn-outline-secondary" onclick="refreshData()">
                        <i class="fas fa-sync me-2"></i>Refresh
                    </button>
                    <a href="{{ url_for('admin.subscription_users') }}" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>View Users
                    </a>
                </div>
            </div>

            <!-- Key Metrics Row -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-gradient-primary text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title text-white-50">Total Subscriptions</h6>
                                    <h2 class="mb-0">{{ analytics.total_subscriptions or 0 }}</h2>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-gradient-success text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title text-white-50">Active Subscriptions</h6>
                                    <h2 class="mb-0">{{ analytics.active_subscriptions or 0 }}</h2>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-gradient-info text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title text-white-50">Trial Users</h6>
                                    <h2 class="mb-0">{{ analytics.trial_subscriptions or 0 }}</h2>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-gradient-warning text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title text-white-50">Monthly MRR</h6>
                                    <h2 class="mb-0">${{ "%.0f"|format(analytics.total_monthly_recurring_revenue or 0) }}</h2>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-dollar-sign fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revenue Breakdown -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                Revenue Breakdown
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary">${{ "%.2f"|format(analytics.monthly_revenue or 0) }}</h4>
                                        <p class="text-muted mb-0">Monthly Revenue</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success">${{ "%.2f"|format(analytics.yearly_revenue or 0) }}</h4>
                                    <p class="text-muted mb-0">Yearly Revenue</p>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <h5 class="text-info">${{ "%.2f"|format(analytics.total_monthly_recurring_revenue or 0) }}</h5>
                                <p class="text-muted mb-0">Total MRR (Monthly Recurring Revenue)</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Subscription Status
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-success rounded-circle" style="width: 12px; height: 12px;"></div>
                                        </div>
                                        <div class="flex-grow-1 ms-2">
                                            <div class="fw-bold">{{ analytics.active_subscriptions or 0 }}</div>
                                            <div class="text-muted small">Active</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-info rounded-circle" style="width: 12px; height: 12px;"></div>
                                        </div>
                                        <div class="flex-grow-1 ms-2">
                                            <div class="fw-bold">{{ analytics.trial_subscriptions or 0 }}</div>
                                            <div class="text-muted small">Trialing</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-danger rounded-circle" style="width: 12px; height: 12px;"></div>
                                        </div>
                                        <div class="flex-grow-1 ms-2">
                                            <div class="fw-bold">{{ analytics.canceled_subscriptions or 0 }}</div>
                                            <div class="text-muted small">Canceled</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-secondary rounded-circle" style="width: 12px; height: 12px;"></div>
                                        </div>
                                        <div class="flex-grow-1 ms-2">
                                            <div class="fw-bold">{{ analytics.total_subscriptions or 0 }}</div>
                                            <div class="text-muted small">Total</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Subscription Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Coming Soon:</strong> Real-time subscription activity feed will be displayed here, 
                                including new subscriptions, cancellations, upgrades, and downgrades.
                            </div>
                            
                            <!-- Placeholder for recent activity -->
                            <div class="text-center py-4">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">Activity Timeline</h6>
                                <p class="text-muted">Recent subscription events will appear here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}
.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}
.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
}
.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function refreshData() {
    // Show loading state
    const refreshBtn = document.querySelector('button[onclick="refreshData()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Refreshing...';
    refreshBtn.disabled = true;
    
    // Simulate refresh (in real implementation, this would make an AJAX call)
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Auto-refresh every 5 minutes
setInterval(() => {
    console.log('Auto-refreshing analytics data...');
    // In a real implementation, this would update the data without full page reload
}, 300000);
</script>
{% endblock %}
