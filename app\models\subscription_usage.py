from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTimeField, IntField, DecimalField
from datetime import datetime, timedelta
from .user import User
from .subscription import Subscription, Feature

class SubscriptionUsage(Document):
    """Track usage of subscription features"""
    user = ReferenceField(User, required=True)
    subscription = ReferenceField(Subscription, required=True)
    feature = ReferenceField(Feature, required=True)
    
    # Usage tracking
    usage_count = IntField(default=0)
    usage_date = DateTimeField(required=True)
    billing_period_start = DateTimeField(required=True)
    billing_period_end = DateTimeField(required=True)
    
    # Additional metadata
    metadata = StringField()  # JSON string for additional data
    
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'subscription_usage',
        'indexes': [
            ('user', 'feature', 'billing_period_start'),
            ('subscription', 'feature'),
            'usage_date'
        ]
    }
    
    @classmethod
    def get_current_usage(cls, user, feature_name, subscription=None):
        """Get current usage for a feature in the current billing period"""
        if not subscription:
            from ..services.subscription_service import SubscriptionService
            subscription = SubscriptionService.get_subscription(str(user.id))
        
        if not subscription:
            return 0
        
        # Calculate current billing period
        now = datetime.utcnow()
        if subscription.billing_cycle.value == 'monthly':
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if period_start.month == 12:
                period_end = period_start.replace(year=period_start.year + 1, month=1) - timedelta(seconds=1)
            else:
                period_end = period_start.replace(month=period_start.month + 1) - timedelta(seconds=1)
        else:  # yearly
            period_start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = period_start.replace(year=period_start.year + 1) - timedelta(seconds=1)
        
        # Get feature
        feature = Feature.objects(name=feature_name).first()
        if not feature:
            return 0
        
        # Sum usage in current period
        usage_records = cls.objects(
            user=user,
            feature=feature,
            billing_period_start__lte=period_start,
            billing_period_end__gte=period_end
        )
        
        return sum(record.usage_count for record in usage_records)
    
    @classmethod
    def increment_usage(cls, user, feature_name, count=1, subscription=None):
        """Increment usage for a feature"""
        if not subscription:
            from ..services.subscription_service import SubscriptionService
            subscription = SubscriptionService.get_subscription(str(user.id))
        
        if not subscription:
            return False
        
        feature = Feature.objects(name=feature_name).first()
        if not feature:
            return False
        
        # Calculate current billing period
        now = datetime.utcnow()
        if subscription.billing_cycle.value == 'monthly':
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if period_start.month == 12:
                period_end = period_start.replace(year=period_start.year + 1, month=1) - timedelta(seconds=1)
            else:
                period_end = period_start.replace(month=period_start.month + 1) - timedelta(seconds=1)
        else:  # yearly
            period_start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = period_start.replace(year=period_start.year + 1) - timedelta(seconds=1)
        
        # Find or create usage record for current period
        usage_record = cls.objects(
            user=user,
            subscription=subscription,
            feature=feature,
            billing_period_start=period_start,
            billing_period_end=period_end
        ).first()
        
        if usage_record:
            usage_record.usage_count += count
            usage_record.usage_date = now
        else:
            usage_record = cls(
                user=user,
                subscription=subscription,
                feature=feature,
                usage_count=count,
                usage_date=now,
                billing_period_start=period_start,
                billing_period_end=period_end
            )
        
        usage_record.save()
        
        # Update subscription current_usage cache
        subscription.current_usage[feature_name] = usage_record.usage_count
        subscription.save()
        
        return True

class SubscriptionInvoice(Document):
    """Invoice records for subscription billing"""
    user = ReferenceField(User, required=True)
    subscription = ReferenceField(Subscription, required=True)
    
    # Invoice details
    invoice_number = StringField(required=True, unique=True)
    amount = DecimalField(required=True, precision=2)
    currency = StringField(default="USD", max_length=3)
    tax_amount = DecimalField(default=0, precision=2)
    total_amount = DecimalField(required=True, precision=2)
    
    # Billing period
    billing_period_start = DateTimeField(required=True)
    billing_period_end = DateTimeField(required=True)
    
    # Invoice status
    status = StringField(choices=['draft', 'sent', 'paid', 'overdue', 'cancelled'], default='draft')
    
    # Payment details
    paid_at = DateTimeField()
    payment_method = StringField()
    transaction_id = StringField()
    
    # External service IDs
    stripe_invoice_id = StringField()
    paypal_invoice_id = StringField()
    
    # Dates
    issued_at = DateTimeField(default=datetime.utcnow)
    due_date = DateTimeField()
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'subscription_invoices',
        'indexes': ['user', 'subscription', 'status', 'invoice_number']
    }
    
    def generate_invoice_number(self):
        """Generate a unique invoice number"""
        import uuid
        timestamp = datetime.utcnow().strftime('%Y%m%d')
        unique_id = str(uuid.uuid4())[:8].upper()
        self.invoice_number = f"INV-{timestamp}-{unique_id}"
        return self.invoice_number
