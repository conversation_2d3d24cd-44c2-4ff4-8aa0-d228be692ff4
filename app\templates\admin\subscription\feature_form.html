{% extends "admin/admin_layout.html" %}

{% block title %}{% if action == 'Create' %}ایجاد{% else %}ویرایش{% endif %} امکانات - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-{% if action == 'Create' %}plus{% else %}edit{% endif %} me-2"></i>
                    {% if action == 'Create' %}ایجاد{% else %}ویرایش{% endif %} امکانات
                </h2>
                <a href="{{ url_for('admin.subscription_features') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>بازگشت به امکانات
                </a>
            </div>

            <form method="POST">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="card-title mb-0">جزئیات امکانات</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">نام امکانات (داخلی) *</label>
                                <input type="text" class="form-control" id="name" name="name"
                                       value="{{ feature.name if feature else '' }}" required>
                                <div class="form-text">برای استفاده داخلی، باید منحصر به فرد باشد (مثال: posts_per_month)</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="display_name" class="form-label">نام نمایشی *</label>
                                <input type="text" class="form-control" id="display_name" name="display_name"
                                       value="{{ feature.display_name if feature else '' }}" required>
                                <div class="form-text">به کاربران نمایش داده می‌شود (مثال: پست در ماه)</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">توضیحات</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="توضیح دهید این امکانات چه کاری انجام می‌دهد...">{{ feature.description if feature else '' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="feature_type" class="form-label">نوع امکانات *</label>
                                <select class="form-select" id="feature_type" name="feature_type" required>
                                    <option value="limit" {% if feature and feature.feature_type.value == 'limit' %}selected{% endif %}>
                                        محدودیت (محدودیت عددی)
                                    </option>
                                    <option value="boolean" {% if feature and feature.feature_type.value == 'boolean' %}selected{% endif %}>
                                        بولین (درست/غلط)
                                    </option>
                                    <option value="quota" {% if feature and feature.feature_type.value == 'quota' %}selected{% endif %}>
                                        سهمیه (بر اساس استفاده)
                                    </option>
                                </select>
                                <div class="form-text">
                                    <small>
                                        <strong>محدودیت:</strong> حداکثر مجاز (مثال: ۱۰۰ پست)<br>
                                        <strong>بولین:</strong> امکانات فعال/غیرفعال<br>
                                        <strong>سهمیه:</strong> استفاده در طول زمان ردیابی می‌شود
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="default_value" class="form-label">مقدار پیش‌فرض *</label>
                                <input type="text" class="form-control" id="default_value" name="default_value"
                                       value="{{ feature.default_value if feature else '0' }}" required>
                                <div class="form-text">مقدار پیش‌فرض برای پلن‌های جدید</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="unit" class="form-label">واحد</label>
                                <input type="text" class="form-control" id="unit" name="unit"
                                       value="{{ feature.unit if feature else '' }}"
                                       placeholder="مثال: پست، کاربر، گیگابایت">
                                <div class="form-text">واحد اندازه‌گیری اختیاری</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>نمونه‌های نوع امکانات</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>امکانات محدودیت:</strong>
                                            <ul class="mb-0">
                                                <li>پست در ماه: ۱۰۰</li>
                                                <li>حساب‌های اجتماعی: ۵</li>
                                                <li>اعضای تیم: ۳</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>امکانات بولین:</strong>
                                            <ul class="mb-0">
                                                <li>پشتیبانی اولویت‌دار: درست</li>
                                                <li>دسترسی آنالیز: غلط</li>
                                                <li>برچسب سفید: درست</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>امکانات سهمیه:</strong>
                                            <ul class="mb-0">
                                                <li>فراخوانی API: ۱۰۰۰۰</li>
                                                <li>فضای ذخیره‌سازی: ۱۰۰۰</li>
                                                <li>پهنای باند: ۱۰۰</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin.subscription_features') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>لغو
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% if action == 'Create' %}ایجاد{% else %}ویرایش{% endif %} امکانات
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const featureTypeSelect = document.getElementById('feature_type');
    const defaultValueInput = document.getElementById('default_value');
    
    function updateDefaultValuePlaceholder() {
        const selectedType = featureTypeSelect.value;
        
        switch(selectedType) {
            case 'limit':
                defaultValueInput.placeholder = 'e.g., 100';
                break;
            case 'boolean':
                defaultValueInput.placeholder = 'true or false';
                break;
            case 'quota':
                defaultValueInput.placeholder = 'e.g., 1000';
                break;
        }
    }
    
    featureTypeSelect.addEventListener('change', updateDefaultValuePlaceholder);
    updateDefaultValuePlaceholder(); // Set initial placeholder
});
</script>
{% endblock %}
